"use client";

import React from "react";
import { UserProfile as UserProfileType } from "@/types/user";
import { Noto_Sans } from "next/font/google";
import ScrollObserver from "@/lib/scroll-observer";

// Import broken-ink components
import HeroParallax from "@/components/broken-ink/HeroParallax";
import SocialMediaSection from "@/components/broken-ink/SocialMediaSection";
import ServicesSection from "@/components/broken-ink/ServicesSection";
import GallerySection from "@/components/broken-ink/GallerySection";
import ArtistsSection from "@/components/broken-ink/ArtistsSection";
import FeaturesSection from "@/components/broken-ink/FeaturesSection";
import GenericSection from "@/components/broken-ink/GenericSection";
import ContactSection from "@/components/broken-ink/ContactSection";
import ReviewsSection from "@/components/broken-ink/ReviewsSection";

const notoSans = Noto_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "700", "900"],
  variable: "--font-noto-sans",
  display: "swap",
});

interface HeroParallaxExampleProps {
  initialProfile: UserProfileType;
}

export function HeroParallaxExample({
  initialProfile,
}: HeroParallaxExampleProps) {
  return (
    <ScrollObserver>
      <div
        className={`min-h-screen w-full bg-black relative ${notoSans.variable}`}
      >
        <main>
          {/* Using the new HeroParallax component instead of HeroSection */}
          <HeroParallax profile={initialProfile} />
          
          {/* Content sections that will scroll over the parallax hero */}
          <div className="relative z-10 bg-black">
            <SocialMediaSection profile={initialProfile} />
            <ServicesSection profile={initialProfile} />
            <GallerySection profile={initialProfile} />
            <ArtistsSection profile={initialProfile} />
            <FeaturesSection profile={initialProfile} />
            <GenericSection profile={initialProfile} />
            <ReviewsSection profile={initialProfile} />
            <ContactSection profile={initialProfile} />
          </div>
        </main>
      </div>
    </ScrollObserver>
  );
}
